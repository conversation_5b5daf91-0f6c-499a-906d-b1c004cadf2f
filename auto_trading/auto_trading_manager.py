"""
PPP.fun 自动交易管理器
统一管理v1和v2版本的自动交易功能
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from config_manager import PPPVersion, get_config
from config_adapter import ConfigProvider
from .models import AutoBuyConfig
from .scheduler import TradingScheduler
from .trader import ConcurrentTrader
from .buyer_factory import create_buyer_adapter
from ppp_api import ProjectDataManager, PPPAPIClient

# 日志配置
from log_config import setup_logging, get_logger

setup_logging(app_name="auto_trading_manager")
logger = get_logger()

class AutoTradingManager:
    """自动交易管理器"""
    
    def __init__(self, config_name: str = "default", version: PPPVersion = PPPVersion.AUTO):
        """初始化自动交易管理器
        
        Args:
            config_name: 配置名称
            version: PPP版本
        """
        self.config_name = config_name
        self.version = version
        self.buyer_adapter = None
        self.scheduler = None
        self.trader = None
        self.project_manager = None
        self.api_client = None
        self._initialized = False
        
        logger.info(f"🎯 自动交易管理器初始化 (配置: {config_name}, 版本: {version.value})")
    
    async def initialize(self):
        """初始化管理器组件"""
        try:
            if self._initialized:
                logger.info("⚠️ 管理器已初始化，跳过")
                return
            
            logger.info("🔧 初始化自动交易管理器组件...")
            
            # 创建买入器适配器
            self.buyer_adapter = create_buyer_adapter(self.config_name, self.version)
            logger.info(f"✅ 买入器适配器创建成功 ({self.buyer_adapter.version})")
            
            # 创建API客户端
            provider = ConfigProvider(self.config_name, self.version)
            version_info = provider.get_version_info()
            self.api_client = PPPAPIClient(timeout=30.0)
            logger.info(f"✅ API客户端创建成功 ({version_info['api_endpoint']})")

            # 创建项目数据管理器
            self.project_manager = ProjectDataManager(self.api_client)
            logger.info("✅ 项目数据管理器创建成功")
            
            # 创建调度器
            self.scheduler = TradingScheduler(
                self.buyer_adapter, 
                self.project_manager
            )
            logger.info("✅ 交易调度器创建成功")
            
            # 创建并发交易器
            self.trader = ConcurrentTrader(
                self.buyer_adapter,
                self.api_client
            )
            logger.info("✅ 并发交易器创建成功")
            
            # 设置调度器的交易器
            self.scheduler.trader = self.trader
            
            self._initialized = True
            logger.info("🎉 自动交易管理器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 自动交易管理器初始化失败: {e}")
            raise
    
    async def start_trading(self, auto_config: AutoBuyConfig) -> Dict[str, Any]:
        """启动自动交易
        
        Args:
            auto_config: 自动交易配置
            
        Returns:
            交易结果
        """
        try:
            if not self._initialized:
                await self.initialize()
            
            logger.info(f"🚀 启动自动交易: {auto_config.name}")
            
            # 配置并发交易器
            if auto_config.retry_settings and auto_config.priority_fee_settings and auto_config.concurrency_settings:
                self.trader.configure(
                    auto_config.concurrency_settings,
                    auto_config.priority_fee_settings,
                    auto_config.retry_settings
                )
                logger.info("⚙️ 并发交易器配置完成")
            
            # 启动交易
            result = await self.scheduler.start_auto_trading(auto_config)
            
            logger.info(f"✅ 自动交易完成: {auto_config.name}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 自动交易失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "config_name": auto_config.name,
                "timestamp": datetime.now().isoformat()
            }
    
    async def stop_trading(self):
        """停止自动交易"""
        try:
            if self.scheduler:
                await self.scheduler.stop()
                logger.info("🛑 自动交易已停止")
        except Exception as e:
            logger.error(f"❌ 停止自动交易失败: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """获取管理器状态"""
        return {
            "initialized": self._initialized,
            "config_name": self.config_name,
            "version": self.version.value,
            "buyer_info": self.buyer_adapter.get_info() if self.buyer_adapter else None,
            "components": {
                "buyer_adapter": self.buyer_adapter is not None,
                "scheduler": self.scheduler is not None,
                "trader": self.trader is not None,
                "project_manager": self.project_manager is not None,
                "api_client": self.api_client is not None
            }
        }
    
    async def close(self):
        """关闭管理器"""
        try:
            if self.scheduler:
                await self.scheduler.stop()

            if self.buyer_adapter and hasattr(self.buyer_adapter, 'close'):
                await self.buyer_adapter.close()

            logger.info("🔒 自动交易管理器已关闭")

        except Exception as e:
            logger.error(f"❌ 关闭管理器失败: {e}")

class AutoTradingManagerFactory:
    """自动交易管理器工厂"""
    
    @staticmethod
    def create_v1_manager(config_name: str = "default") -> AutoTradingManager:
        """创建v1自动交易管理器"""
        return AutoTradingManager(config_name, PPPVersion.V1)
    
    @staticmethod
    def create_v2_manager(config_name: str = "default") -> AutoTradingManager:
        """创建v2自动交易管理器"""
        return AutoTradingManager(config_name, PPPVersion.V2)
    
    @staticmethod
    def create_auto_manager(config_name: str = "default") -> AutoTradingManager:
        """创建自动检测版本的管理器"""
        return AutoTradingManager(config_name, PPPVersion.AUTO)
    
    @staticmethod
    def create_from_config(auto_config: AutoBuyConfig, 
                          version: PPPVersion = PPPVersion.AUTO) -> AutoTradingManager:
        """从自动交易配置创建管理器"""
        # 这里可以根据配置内容推断版本或使用指定版本
        return AutoTradingManager("default", version)

async def run_auto_trading(config_file_path: str, 
                          version: PPPVersion = PPPVersion.AUTO) -> Dict[str, Any]:
    """运行自动交易的便捷函数
    
    Args:
        config_file_path: 配置文件路径
        version: PPP版本
        
    Returns:
        交易结果
    """
    try:
        # 加载配置
        auto_config = AutoBuyConfig.load_from_file(config_file_path)
        logger.info(f"📋 加载配置: {auto_config.name}")
        
        # 创建管理器
        manager = AutoTradingManager("default", version)
        
        try:
            # 启动交易
            result = await manager.start_trading(auto_config)
            return result
        finally:
            # 确保关闭管理器
            await manager.close()
            
    except Exception as e:
        logger.error(f"❌ 运行自动交易失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "config_file": config_file_path,
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    # 测试自动交易管理器
    async def test_manager():
        print("🔧 测试自动交易管理器...")
        
        try:
            # 创建v2管理器
            manager = AutoTradingManagerFactory.create_v2_manager("test_v2_independent")
            
            # 初始化
            await manager.initialize()
            
            # 获取状态
            status = await manager.get_status()
            print(f"✅ 管理器状态: {status}")
            
            # 关闭
            await manager.close()
            
            print("🎉 自动交易管理器测试成功")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    asyncio.run(test_manager())

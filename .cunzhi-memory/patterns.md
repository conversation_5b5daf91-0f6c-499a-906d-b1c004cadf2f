# 常用模式和最佳实践

- Bark推送模块实现完成：创建了完整功能的bark_notifier.py模块，支持所有Bark API功能（基础推送、高级功能、加密推送、批量推送、模板系统）。关键特性：1)业务安全-推送失败不影响主业务流程；2)异步执行-后台线程推送不阻塞主程序；3)错误隔离-所有异常被安全捕获；4)日志集成-与现有log_config.py完美集成；5)便捷函数-bark_send/bark_success/bark_error等安全包装器。已更新.env配置文件添加BARK_KEY支持，创建使用示例和集成文档。模块通过测试，可直接在项目中使用。
- Split NFT功能实现完成：在ppp_simple_buyer.py中实现了完整的split_nft方法，包括参数验证、PDA计算、Anchor交易构建和错误处理。实现了build_split_transaction_with_anchor和calculate_split_pda_addresses方法。在run_simple_buyer.py中添加了分割NFT的干运行和真实分割选项。Split功能支持将一个NFT分割成两个NFT，需要满足价格至少是初始价格2倍的条件。
- Split NFT PDA计算优化：修正了calculate_split_pda_addresses方法，现在从API动态获取项目的当前最大NFT ID，然后计算新NFT ID为max_id+1。这确保了新NFT ID的正确性，避免了硬编码假设。如果API调用失败，使用备用方案（当前NFT ID + 1000）。方法改为异步以支持API调用。
- Split NFT API调用优化：修正了calculate_split_pda_addresses方法，现在使用正确的API端点 /project/{project_mint} 来获取项目的当前nft_id字段，而不是通过NFT列表计算最大ID。这确保了获取到的是项目当前的准确nft_id，新NFT ID计算为current_nft_id + 1。
- Split NFT API接口修正：使用正确的 /project/{project_mint} 接口获取项目的nft_id字段。project_mint就是project_id，直接作为路径参数使用。这个接口返回的数据中包含当前项目的nft_id，用于计算新NFT的ID（current_nft_id + 1）。

#!/usr/bin/env python3
"""
PPP.fun 简化买入器启动脚本
提供交互式菜单来选择不同的操作
"""

import asyncio
import sys
import argparse
import logging
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

# 日志配置
from log_config import setup_buyer_logging, get_logger, set_project_name

# 初始化日志配置
setup_buyer_logging()
logger = get_logger()

# 设置第三方库的日志级别
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

from ppp_simple_buyer import PPPSimpleBuyer
from simple_config import load_config
from ppp_api import PPPAPIClient, ProjectDataManager
from auto_trading.models import AutoBuyConfig
from auto_trading.scheduler import TradingScheduler
from auto_trading.trader import ConcurrentTrader

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 PPP.fun 简化买入器")
    print("=" * 60)
    print("基于AnchorPy的重新实现版本")
    print("支持干运行模式和真实购买")
    print("=" * 60)

def print_menu():
    """打印菜单"""
    print("\n📋 请选择操作:")
    print("1. 测试功能（推荐首次使用）")
    print("2. 购买NFT #1（干运行）")
    print("3. 购买NFT #1（真实购买）")
    print("4. 查看NFT信息")
    print("5. 检查钱包余额")
    print("0. 退出")
    print("-" * 40)

async def test_functionality(expected_values_file: str = "expected_values/死神_32.json", nft_id: int = 32, config_file: str = "auto_buy_configs/死神_20250803_233712.json"):
    """测试功能"""
    print(f"\n🧪 开始功能测试... NFT ID: {nft_id}")
    if expected_values_file:
        print(f"📁 使用期望值文件: {expected_values_file}")

    try:
        config = load_config()
        async with PPPSimpleBuyer(config.private_key) as buyer:
            print("✅ 买入器初始化成功")

            # 测试NFT数据加载 - 优先使用API
            nft_info = None
            project_mint = None
            project_id = None
            creator = None

            # 从配置文件中获取项目mint地址
            if config_file:
                try:
                    auto_config = AutoBuyConfig.from_file(config_file)
                    # 查找包含指定NFT ID的目标项目
                    for target in auto_config.targets:
                        if nft_id in target.target_nft_ids:
                            project_mint = target.mint_pubkey
                            creator = target.creator
                            project_id = target.project_id
                            print(f"🔍 从配置文件找到NFT #{nft_id}的项目mint: {project_mint}")
                            break

                    if not project_mint:
                        print(f"⚠️ 配置文件中未找到NFT #{nft_id}的项目信息")
                except Exception as e:
                    print(f"⚠️ 读取配置文件失败: {e}")
            else:
                print(f"⚠️ 未提供配置文件")

            # 如果配置文件中没有找到，尝试从期望值文件中获取项目mint
            if not project_mint and expected_values_file:
                try:
                    with open(expected_values_file, 'r') as f:
                        expected_data = json.load(f)
                    project_mint = expected_data.get("outputTokenMint")
                    if project_mint:
                        print(f"🔍 从期望值文件获取项目mint: {project_mint}")
                    else:
                        print(f"⚠️ 期望值文件中未找到outputTokenMint字段")
                except Exception as e:
                    print(f"⚠️ 读取期望值文件失败: {e}")

            if not project_mint:
                print(f"⚠️ 无法确定项目mint地址，将从所有缓存文件中查找...")

            # 尝试从API获取（如果有项目mint地址）
            if project_mint:
                print(f"🔍 从API获取NFT #{nft_id} (项目: {project_mint})")
                nft_info = await buyer.find_nft_by_id_from_api(project_mint, nft_id)

            # 如果API失败或没有项目mint，回退到缓存
            if not nft_info:
                print(f"🔍 从本地缓存获取NFT #{nft_id}")
                nft_info = buyer.find_nft_by_id(nft_id, project_mint)

            if nft_info:
                print(f"✅ 找到NFT #{nft_id}: {nft_info.get('nft_pubkey', 'N/A')}")
            else:
                print(f"❌ 未找到NFT #{nft_id}")
                return

            # 测试交易构建（如果指定了期望值文件，则使用它进行对比）
            try:
                if expected_values_file:
                    # 临时修改默认期望值文件
                    original_method = buyer.compare_parameters_with_expected
                    async def compare_with_custom_file(calculated_accounts, config_file=None):
                        return await original_method(calculated_accounts, expected_values_file)
                    buyer.compare_parameters_with_expected = compare_with_custom_file

                # 构建买入参数 - 使用v2项目
                buy_params = {
                    "nft_id": nft_id,
                    "creator": creator,  # 通用creator
                    "dry_run": True,
                    "project_mint": project_mint, 
                    "project_id": project_id,
                    "enable_validation": True  # 启用验证以进行参数对比
                }

                # 传递买入参数给交易构建
                transaction = await buyer.build_buy_transaction_with_anchor(buy_params, nft_info)
                print("✅ 交易构建成功")
            except Exception as e:
                print(f"❌ 交易构建失败: {e}")
                return

            print("🎉 所有测试通过！")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def buy_nft_dry_run():
    """干运行购买NFT #83"""
    print("\n🔍 干运行购买NFT #1...")
    
    try:
        config = load_config()
        async with PPPSimpleBuyer(config.private_key) as buyer:
            # 注意：需要提供creator参数，这里使用CAI项目的creator
            buy_params = {
                "nft_id": 32,
                "creator": "7nFTyt8spGgNS7MdL9MCcSrwAhPV9HVhZFZZgiboUU4z",  # CAI项目的creator
                "dry_run": True,
                "project_mint": "C1LhkbrjMh7P1Zur8H1MJ6SuQwRxAWB7zJZauTFEVppp",  # CAI项目mint地址
                "project_id": "BVcfWeHQU5HtWFa1GXo561VfWy4uu4jZJNPXBvsn4WDk",
            }
            result = await buyer.buy_nft(buy_params)
            
            if result["success"]:
                print("✅ 干运行成功！")
                print(f"   模拟交易: {result['transaction_signature']}")
            else:
                print(f"❌ 干运行失败: {result['error_message']}")
                
    except Exception as e:
        print(f"❌ 干运行出错: {e}")

async def buy_nft_real():
    """真实购买NFT #1"""
    print("\n⚠️  真实购买NFT #1")
    print("这将花费真实的SOL！")
    
    confirm = input("确认继续？(输入 'YES' 确认): ")
    if confirm != "YES":
        print("❌ 购买已取消")
        return
    
    try:
        config = load_config()
        async with PPPSimpleBuyer(config.private_key) as buyer:
            # 检查余额
            await buyer._display_wallet_balance()
            # 获取余额用于后续检查
            try:
                balance_response = await buyer.client.get_balance(buyer.wallet_keypair.pubkey())
                balance_sol = balance_response.value / 1_000_000_000
            except:
                print("⚠️ 无法获取余额，继续执行...")
                balance_sol = 1.0  # 假设有足够余额
            
            # 获取NFT价格
            nft_info = buyer.find_nft_by_id(83, "9U1DCq89ybinSytPNn1sfDbn1iv3MGPhGoyV3BvPSppp")
            if not nft_info:
                print("❌ 未找到NFT #83")
                return
            
            nft_price_sol = nft_info['price'] / 1_000_000_000
            print(f"💎 NFT价格: {nft_price_sol:.4f} SOL")
            
            if balance_sol < nft_price_sol + 0.01:
                print("❌ 余额不足！")
                return
            
            # 执行购买
            buy_params = {
                "nft_id": 1,
                "creator": "U9cGTCZGhMEwtjoyX1cB2LL7Sq1FCsNhAsVTCB5Judy",  # CAI项目的creator
                "dry_run": False,
                "project_mint": "63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp"  # CAI项目mint地址
            }
            result = await buyer.buy_nft(buy_params)
            
            if result["success"]:
                print("🎉 购买成功！")
                print(f"   交易签名: {result['transaction_signature']}")
                print(f"   查看交易: https://solscan.io/tx/{result['transaction_signature']}")
            else:
                print(f"❌ 购买失败: {result['error_message']}")
                
    except Exception as e:
        print(f"❌ 购买出错: {e}")

async def show_nft_info():
    """显示NFT信息"""
    print("\n📋 NFT #1 信息:")
    
    try:
        config = load_config()
        async with PPPSimpleBuyer(config.private_key) as buyer:
            nft_info = buyer.find_nft_by_id(1, "63B7LWnQDwAPybnjpiLrEEx1cVGG9jwxmQBc3BAMWppp")
            
            if nft_info:
                print(f"   NFT地址: {nft_info['nft_pubkey']}")
                print(f"   当前拥有者: {nft_info['owner_pubkey']}")
                print(f"   价格: {nft_info['price']} lamports ({nft_info['price']/1_000_000_000:.4f} SOL)")
                print(f"   轮次: {nft_info['round']}")
                print(f"   项目mint: {nft_info['mint_pubkey']}")
                print(f"   分割次数: {nft_info['split_count']}")
                print(f"   最后交易: {nft_info['last_trade']}")
            else:
                print("❌ 未找到NFT #83")
                
    except Exception as e:
        print(f"❌ 获取信息失败: {e}")



async def check_balance():
    """检查钱包余额"""
    print("\n💰 检查钱包余额...")
    
    try:
        config = load_config()
        async with PPPSimpleBuyer(config.private_key) as buyer:
            print(f"   钱包地址: {buyer.wallet_keypair.pubkey()}")
            await buyer._display_wallet_balance()
            
    except Exception as e:
        print(f"❌ 检查余额失败: {e}")

async def main():
    """主函数"""
    print_banner()
    
    # 检查必要文件
    required_files = ["abi.json"]
    missing_files = [f for f in required_files if not Path(f).exists()]

    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\n请确保以下文件存在:")
        print("   - abi.json: PPP.fun程序的ABI文件")
        print("\n💡 提示: NFT数据现在通过API动态获取，不再需要本地文件")
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选项 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                await test_functionality()
            elif choice == "2":
                await buy_nft_dry_run()
            elif choice == "3":
                await buy_nft_real()
            elif choice == "4":
                await show_nft_info()
            elif choice == "5":
                await check_balance()
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出")
            break
        except Exception as e:
            print(f"❌ 操作出错: {e}")

async def start_auto_trading(config_file_path: str):
    """启动自动交易功能

    Args:
        config_file_path: 自动交易配置文件路径
    """
    try:
        # 设置全局项目名称
        project_name = Path(config_file_path).stem
        set_project_name(project_name)

        # 初始化日志配置（基于配置文件路径）
        from log_config import setup_auto_trading_logging
        setup_auto_trading_logging(config_file_path)

        print("🚀 启动自动交易功能...")
        logger.info(f"启动自动交易功能，配置文件: {config_file_path}")

        # 加载自动交易配置
        print(f"📋 加载配置文件: {config_file_path}")
        auto_config = AutoBuyConfig.from_file(config_file_path)

        # 验证配置
        validation_errors = auto_config.validate()
        if validation_errors:
            print("❌ 配置验证失败:")
            for error in validation_errors:
                print(f"   - {error}")
            return

        print(f"✅ 配置验证通过: {auto_config.name}")
        print(f"📝 配置描述: {auto_config.description}")
        print(f"🎯 购买目标数量: {len(auto_config.targets)}")
        print(f"🔍 干运行模式: {auto_config.dry_run}")

        # 显示目标信息
        for i, target in enumerate(auto_config.targets, 1):
            print(f"   目标{i}: {target.project_name}")
            print(f"     项目mint: {target.mint_pubkey}")
            print(f"     目标NFT IDs: {target.target_nft_ids}")
            print(f"     最大数量: {target.max_quantity}")

        # 交互式确认
        if not auto_config.dry_run:
            print("\n⚠️  警告: 这将执行真实的NFT购买交易!")
            confirm = input("确认继续吗? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("❌ 用户取消操作")
                return
        else:
            print("\n🔍 干运行模式，不会执行真实交易")
            confirm = input("开始自动交易吗? (Y/n): ").strip().lower()
            if confirm in ['n', 'no']:
                print("❌ 用户取消操作")
                return

        # 加载简单配置（优先使用AutoBuyConfig中的私钥）
        simple_config = load_config(auto_config)

        # 创建组件实例
        async with PPPAPIClient() as api_client:
            project_manager = ProjectDataManager(api_client)

            async with PPPSimpleBuyer(simple_config.private_key) as buyer:
                # 创建交易执行器
                trader = ConcurrentTrader(buyer, api_client)

                # 配置交易执行器（提供默认值）
                from auto_trading.models import ConcurrencySettings, PriorityFeeSettings, RetrySettings

                concurrency_settings = auto_config.concurrency_settings or ConcurrencySettings()
                priority_fee_settings = auto_config.priority_fee_settings or PriorityFeeSettings()
                retry_settings = auto_config.retry_settings or RetrySettings()

                trader.configure(
                    concurrency_settings,
                    priority_fee_settings,
                    retry_settings
                )

                # 创建调度器（传入trader以支持随机购买）
                scheduler = TradingScheduler(buyer, project_manager, trader)

                print("✅ 所有组件初始化完成")

                # 启动自动交易
                print("\n🚀 开始自动交易...")
                trading_result = await scheduler.start_auto_trading(auto_config)

                # 显示最终结果
                print("\n📊 自动交易完成!")
                print("=" * 50)
                print(f"配置名称: {trading_result['config_name']}")
                print(f"处理目标: {trading_result['targets_processed']}")
                print(f"总成功购买: {trading_result['total_successful_purchases']}")
                print(f"总失败购买: {trading_result['total_failed_purchases']}")

                if trading_result['errors']:
                    print(f"错误数量: {len(trading_result['errors'])}")
                    print("错误详情:")
                    for error in trading_result['errors'][:5]:  # 只显示前5个错误
                        print(f"   - {error}")
                    if len(trading_result['errors']) > 5:
                        print(f"   ... 还有 {len(trading_result['errors']) - 5} 个错误")

                print("=" * 50)

    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_file_path}")
    except Exception as e:
        print(f"❌ 自动交易失败: {e}")
        import traceback
        traceback.print_exc()

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="PPP.fun 简化买入器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 交互模式
  python run_simple_buyer.py auto_buy_configs/test_project_83.json

  # 直接执行功能测试
  python run_simple_buyer.py auto_buy_configs/test_project_83.json --action test

  # 直接执行干运行
  python run_simple_buyer.py auto_buy_configs/test_project_83.json --action dry-run

  # 直接执行真实购买
  python run_simple_buyer.py auto_buy_configs/test_project_83.json --action buy

  # 启动自动交易
  python run_simple_buyer.py auto_buy_configs/test_project_83.json --action auto-trade

  # 指定期望值文件进行参数对比
  python run_simple_buyer.py auto_buy_configs/test_project_83.json --action test --expected-values expected_values/7X7Qhq4eLmMVfkUyLxZheaQ9oSaGDazNpWFUJfsjSppp_105.json

  # 测试不同的NFT ID
  python run_simple_buyer.py auto_buy_configs/test_project_7X7_105.json --action test --nft-id 105 --expected-values expected_values/7X7Qhq4eLmMVfkUyLxZheaQ9oSaGDazNpWFUJfsjSppp_105.json
        """
    )

    parser.add_argument(
        "config_file",
        nargs="?",
        help="配置文件路径（可选，用于新版本功能）"
    )

    parser.add_argument(
        "--action", "-a",
        choices=["test", "dry-run", "buy", "balance", "validate", "auto-trade"],
        help="直接执行指定操作，跳过交互菜单"
    )

    parser.add_argument(
        "--expected-values", "-e",
        help="期望值配置文件路径（用于参数对比）"
    )

    parser.add_argument(
        "--nft-id", "-n",
        type=int,
        default=83,
        help="要测试的NFT ID（默认: 83）"
    )

    return parser.parse_args()

def main_with_args():
    """带参数解析的主函数"""
    args = parse_arguments()

    # 如果提供了配置文件和action，使用新的逻辑
    if args.config_file and args.action:
        if not Path(args.config_file).exists():
            print(f"❌ 配置文件不存在: {args.config_file}")
            sys.exit(1)

        print_banner()

        try:
            if args.action == "test":
                print("🧪 执行功能测试...")
                asyncio.run(test_functionality(args.expected_values, args.nft_id, args.config_file))
            elif args.action == "dry-run":
                print("🔍 执行干运行测试...")
                # 这里需要实现新的干运行逻辑
                print("干运行功能开发中...")
            elif args.action == "buy":
                print("💰 执行真实购买...")
                # 这里需要实现新的购买逻辑
                print("购买功能开发中...")
            elif args.action == "balance":
                print("📊 查看余额...")
                asyncio.run(check_balance())
            elif args.action == "validate":
                print("🔧 验证配置...")
                print("配置验证功能开发中...")
            elif args.action == "auto-trade":
                print("🤖 启动自动交易...")
                asyncio.run(start_auto_trading(args.config_file))
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
        except Exception as e:
            print(f"❌ 执行出错: {e}")
        return

    # 否则使用原有的交互模式
    asyncio.run(main())

if __name__ == "__main__":
    main_with_args()

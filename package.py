#!/usr/bin/env python3
"""
PPP.fun 自动交易工具打包脚本
创建源码压缩包供分发使用
"""

import os
import shutil
import zipfile
from datetime import datetime
from pathlib import Path

def create_package():
    """创建源码压缩包"""
    
    # 获取当前时间作为版本号
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"ppp_auto_trading_{timestamp}"
    
    # 创建临时目录
    temp_dir = Path("temp_package")
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()
    
    package_dir = temp_dir / package_name
    package_dir.mkdir()
    
    # 需要包含的文件和目录
    include_files = [
        # 核心Python文件
        "ppp_simple_buyer.py",
        "ppp_api.py", 
        "simple_config.py",
        "log_config.py",
        "auto_trading_cli.py",
        "run_simple_buyer.py",
        "start_trading.py",
        
        # 配置文件
        "requirements.txt",
        ".env.example",

        # ABI文件（v2版本）
        "abi.json",
        "abi_complete.json",
        
        # 自动交易模块
        "auto_trading/",
        
    ]
    
    # 需要排除的文件和目录
    exclude_patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo", 
        ".git",
        ".gitignore",
        "logs/",
        "api_result/",
        "project_data/",
        "auto_buy_configs/",
        "expected_values/",
        "temp_package/",
        "*.zip",
        "package.py"
    ]
    
    print(f"📦 开始创建源码包: {package_name}")
    
    # 复制文件
    for item in include_files:
        src_path = Path(item)
        if src_path.exists():
            dst_path = package_dir / item
            
            if src_path.is_file():
                # 复制文件
                dst_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dst_path)
                print(f"✅ 复制文件: {item}")
            elif src_path.is_dir():
                # 复制目录（排除不需要的文件）
                copy_directory(src_path, dst_path, exclude_patterns)
                print(f"✅ 复制目录: {item}")
        else:
            print(f"⚠️ 文件不存在，跳过: {item}")
    
    # 创建README文件
    create_readme(package_dir)
    
    # 创建启动脚本
    create_startup_scripts(package_dir)
    
    # 创建压缩包
    zip_path = f"{package_name}.zip"
    create_zip(temp_dir, package_name, zip_path)
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print(f"🎉 打包完成: {zip_path}")
    print(f"📁 包大小: {os.path.getsize(zip_path) / 1024 / 1024:.2f} MB")

def copy_directory(src_dir, dst_dir, exclude_patterns):
    """复制目录，排除指定模式的文件"""
    dst_dir.mkdir(parents=True, exist_ok=True)
    
    for item in src_dir.rglob("*"):
        # 检查是否应该排除
        relative_path = item.relative_to(src_dir)
        should_exclude = False
        
        for pattern in exclude_patterns:
            if pattern.endswith("/"):
                # 目录模式
                if pattern[:-1] in str(relative_path).split("/"):
                    should_exclude = True
                    break
            elif "*" in pattern:
                # 通配符模式
                import fnmatch
                if fnmatch.fnmatch(item.name, pattern):
                    should_exclude = True
                    break
            else:
                # 精确匹配
                if pattern in str(relative_path):
                    should_exclude = True
                    break
        
        if should_exclude:
            continue
            
        dst_item = dst_dir / relative_path
        
        if item.is_file():
            dst_item.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(item, dst_item)

def create_readme(package_dir):
    """创建README文件"""
    readme_content = """# PPP.fun 自动交易工具

## 简介
这是一个用于 PPP.fun 平台的自动化NFT交易工具，支持自动购买、批量交易等功能。

## 安装依赖
```bash
pip install -r requirements.txt
```

## 配置
1. 复制 `.env.example` 为 `.env`
2. 编辑 `.env` 文件，设置您的钱包私钥和其他配置

## 使用方法

### 1. 简单购买
```bash
python run_simple_buyer.py
```

### 2. 自动交易
```bash
python auto_trading_cli.py
```

### 3. 启动交易（快捷方式）
```bash
python start_trading.py
```

## 注意事项
- 请确保钱包有足够的SOL余额
- 建议先使用干运行模式测试
- 注意网络连接和RPC节点稳定性

## 支持
如有问题请联系开发者。
"""
    
    with open(package_dir / "README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def create_startup_scripts(package_dir):
    """创建启动脚本"""
    
    # Windows批处理文件
    bat_content = """@echo off
echo PPP.fun 自动交易工具
echo.
echo 1. 简单购买模式
echo 2. 自动交易模式
echo 3. 退出
echo.
set /p choice=请选择模式 (1-3): 

if "%choice%"=="1" (
    python run_simple_buyer.py
) else if "%choice%"=="2" (
    python auto_trading_cli.py
) else if "%choice%"=="3" (
    exit
) else (
    echo 无效选择
    pause
)
"""
    
    with open(package_dir / "start.bat", "w", encoding="utf-8") as f:
        f.write(bat_content)
    
    # Linux/Mac shell脚本
    sh_content = """#!/bin/bash
echo "PPP.fun 自动交易工具"
echo ""
echo "1. 简单购买模式"
echo "2. 自动交易模式" 
echo "3. 退出"
echo ""
read -p "请选择模式 (1-3): " choice

case $choice in
    1)
        python3 run_simple_buyer.py
        ;;
    2)
        python3 auto_trading_cli.py
        ;;
    3)
        exit 0
        ;;
    *)
        echo "无效选择"
        ;;
esac
"""
    
    with open(package_dir / "start.sh", "w", encoding="utf-8") as f:
        f.write(sh_content)
    
    # 设置执行权限
    os.chmod(package_dir / "start.sh", 0o755)

def create_zip(temp_dir, package_name, zip_path):
    """创建ZIP压缩包"""
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        package_dir = temp_dir / package_name
        for file_path in package_dir.rglob("*"):
            if file_path.is_file():
                arcname = file_path.relative_to(temp_dir)
                zipf.write(file_path, arcname)

if __name__ == "__main__":
    create_package()

{"version": "2.0.0", "name": "pppfun", "instructions": [{"name": "burn", "accounts": [{"name": "cp_swap_program", "isMut": false, "isSigner": false}, {"name": "amm_config", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": false}, {"name": "pool_state", "isMut": true, "isSigner": false}, {"name": "token0_mint", "isMut": false, "isSigner": false}, {"name": "token1_mint", "isMut": false, "isSigner": false}, {"name": "lp_mint", "isMut": true, "isSigner": false}, {"name": "creator_token0", "isMut": true, "isSigner": false}, {"name": "creator_token1", "isMut": true, "isSigner": false}, {"name": "creator_lp_token", "isMut": true, "isSigner": false}, {"name": "token0_vault", "isMut": true, "isSigner": false}, {"name": "token1_vault", "isMut": true, "isSigner": false}, {"name": "create_pool_fee", "isMut": true, "isSigner": false}, {"name": "observation_state", "isMut": true, "isSigner": false}, {"name": "token_program", "isMut": false, "isSigner": false}, {"name": "token0_program", "isMut": false, "isSigner": false}, {"name": "token1_program", "isMut": false, "isSigner": false}, {"name": "associated_token_program", "isMut": false, "isSigner": false}, {"name": "system_program", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "nft", "isMut": true, "isSigner": false}, {"name": "creator", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "output_token_account", "isMut": true, "isSigner": false}, {"name": "pool_token_vault", "isMut": true, "isSigner": false}, {"name": "pool_wsol_vault", "isMut": true, "isSigner": false}, {"name": "protocol_fee", "isMut": true, "isSigner": false}], "args": []}, {"name": "buy", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": false}, {"name": "nft", "isMut": true, "isSigner": false}, {"name": "current_owner", "isMut": true, "isSigner": false}, {"name": "protocol_fee", "isMut": true, "isSigner": false}, {"name": "referral", "isMut": true, "isSigner": false}, {"name": "creator", "isMut": true, "isSigner": false}, {"name": "system_program", "isMut": false, "isSigner": false}], "args": []}, {"name": "create", "accounts": [{"name": "new_project", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": false}, {"name": "metadata", "isMut": true, "isSigner": false}, {"name": "token_metadata_program", "isMut": false, "isSigner": false}, {"name": "mint", "isMut": true, "isSigner": true}, {"name": "pool_token_vault", "isMut": true, "isSigner": false}, {"name": "wsol_mint", "isMut": false, "isSigner": false}, {"name": "pool_wsol_vault", "isMut": true, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "token_program", "isMut": false, "isSigner": false}, {"name": "associated_token_program", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "system_program", "isMut": false, "isSigner": false}], "args": [{"name": "project_name", "type": "string"}, {"name": "project_desc", "type": "string"}, {"name": "token_symbol", "type": "string"}, {"name": "token_uri", "type": "string"}, {"name": "init_nft_price", "type": "u64"}, {"name": "increase_per_round", "type": "u64"}, {"name": "trade_fee_rate", "type": "u64"}, {"name": "sec_per_round", "type": "u64"}, {"name": "sec_to_burn_nft", "type": "u64"}, {"name": "init_nft_supply", "type": "u64"}, {"name": "max_nft_supply", "type": "u64"}, {"name": "pool_token_pct", "type": "u64"}, {"name": "nft_token_pct", "type": "u64"}]}, {"name": "mint", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "authority", "isMut": true, "isSigner": false}, {"name": "new_nft", "isMut": true, "isSigner": false}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "system_program", "isMut": false, "isSigner": false}], "args": []}, {"name": "split", "accounts": [{"name": "new_nft", "isMut": true, "isSigner": false}, {"name": "old_nft", "isMut": true, "isSigner": false}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "system_program", "isMut": false, "isSigner": false}], "args": []}]}
"""
PPP.fun 简化买入器 - 使用AnchorPy重新实现
基于IDL文件和真实交易参数构建正确的交易
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Optional, Dict, Any, List
from decimal import Decimal

from anchorpy import Program, Provider, Wallet, Context
from anchorpy.error import ProgramError
from solana.rpc.async_api import AsyncClient
from solana.rpc.types import TxOpts
from solana.rpc.commitment import Confirmed
from solders.transaction import Transaction
from solders.message import Message
from solders.pubkey import Pubkey
from solders.keypair import Keypair
from solders.system_program import ID as SYSTEM_PROGRAM_ID
from spl.token.constants import TOKEN_PROGRAM_ID, ASSOCIATED_TOKEN_PROGRAM_ID
from spl.token.instructions import get_associated_token_address
from solana.exceptions import SolanaRpcException
from solana.rpc.core import RPCException
# 日志配置
from log_config import setup_buyer_logging, get_logger

# 初始化日志配置（使用全局项目名称）
setup_buyer_logging()
logger = get_logger()

class PPPSimpleBuyer:
    """PPP.fun 简化买入器"""

    def __init__(self, private_key: str, rpc_url: str = "https://api.mainnet-beta.solana.com"):
        self.rpc_url = rpc_url
        self.client: Optional[AsyncClient] = None
        self.provider: Optional[Provider] = None
        self.program: Optional[Program] = None

        # 清理私钥字符串，移除可能的空格和换行符
        private_key = private_key.strip()
        logger.info(f"私钥长度: {len(private_key)}")
        logger.info(f"私钥前10个字符: {private_key[:10]}...")

        try:
            self.wallet_keypair = Keypair.from_base58_string(private_key)
        except Exception as e:
            logger.error(f"私钥解析失败: {e}")
            logger.error(f"私钥内容: {repr(private_key)}")
            raise ValueError(f"私钥格式不正确: {e}")

        # 从IDL文件加载程序地址
        self.program_id = Pubkey.from_string("PPPtBKg4ggAdZ1KRKv7wGEJoUq8BX79Eh4fKEkgQsJ7")

        # 固定地址（PPP.fun生态系统常量）
        self.wsol_mint = Pubkey.from_string("So********************************111111112")  # WSOL代币
        self.cp_swap_program = Pubkey.from_string("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C")  # Raydium CP程序
        self.protocol_fee_address = "DFShmtUPnJaxKSX6wg2zbyWEAVs9HuBeKNeGihg8o2oV"  # PPP.fun协议费地址

        # 推荐人地址常量
        self.default_referral = "********************************************"
        self.fallback_referral = "NutPmLZUbFx2ULw4pbb3wVqyq2wr7BZc9ZPKMgNhdny"

        # 推荐人地址在初始化时确定
        self.referral_address = self.get_referral_address()

        # 错误代码映射（从完整ABI加载）
        self.error_codes = self._load_error_codes()

        logger.info(f"钱包地址: {self.wallet_keypair.pubkey()}")
        logger.info(f"程序ID: {self.program_id}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def initialize(self):
        """初始化连接和程序"""
        try:
            # 创建RPC连接
            self.client = AsyncClient(self.rpc_url, commitment=Confirmed)

            # 创建钱包和提供者
            wallet = Wallet(self.wallet_keypair)
            self.provider = Provider(self.client, wallet)

            # 加载IDL并创建程序实例 - 使用最佳实践
            try:
                from anchorpy import Idl

                # 读取简化的ABI文件（用于AnchorPy兼容性）
                # 注意：完整的ABI信息在 abi_complete.json 中
                with open("abi.json", "r") as f:
                    raw_idl = f.read()

                # 创建Idl对象
                idl = Idl.from_json(raw_idl)

                # 创建程序实例
                self.program = Program(
                    idl=idl,
                    program_id=self.program_id,
                    provider=self.provider
                )
                logger.info("✅ IDL加载成功，使用Anchor程序实例")
                logger.info(f"程序名称: {idl.name}")

                # 查询并显示钱包余额
                await self._display_wallet_balance()
                logger.info(f"指令数量: {len(idl.instructions)}")

            except Exception as e:
                logger.error(f"IDL加载失败: {e}")
                raise RuntimeError(f"必须使用AnchorPy！IDL加载失败: {e}")


            logger.info("✅ 程序初始化完成")

        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise

    async def _display_wallet_balance(self):
        """查询并显示钱包余额"""
        try:
            # 获取SOL余额
            balance_response = await self.client.get_balance(self.wallet_keypair.pubkey())
            balance_lamports = balance_response.value
            balance_sol = balance_lamports / 1_000_000_000  # 转换为SOL

            logger.info(f"💰 钱包余额: {balance_sol:.2f} SOL")

        except Exception as e:
            logger.warning(f"⚠️ 获取钱包余额失败: {e}")

    async def close(self):
        """关闭连接"""
        if self.client:
            await self.client.close()

    async def burn_nft(self, burn_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        销毁NFT

        Args:
            burn_params: 销毁参数字典，包含：
                - nft_id (int): NFT ID
                - project_mint (str): 项目mint地址
                - dry_run (bool, optional): 是否为干运行模式，默认True
        """
        try:
            # 提取必需参数进行基本验证
            nft_id = burn_params["nft_id"]
            project_mint_str = burn_params["project_mint"]

            logger.info(f"🔥 开始销毁NFT #{nft_id}")

            # 检查是否为干运行模式
            dry_run = burn_params.get("dry_run", True)
            if dry_run:
                # 干运行模式，只进行模拟
                await self.build_burn_transaction_with_anchor(burn_params)
                logger.info("🔍 干运行模式，不会实际发送交易")
                return {
                    "success": True,
                    "transaction_signature": "dry_run_simulation",
                    "nft_id": nft_id,
                    "message": "销毁干运行成功"
                }

            # 构建销毁指令
            instruction = await self.build_burn_transaction_with_anchor(burn_params)

            # 创建交易并签名
            # 获取最新的区块哈希
            recent_blockhash = await self.client.get_latest_blockhash()
            recent_blockhash = recent_blockhash.value.blockhash

            # 创建交易
            from solders.message import Message
            payer = self.wallet_keypair.pubkey()
            message = Message.new_with_blockhash([instruction], payer, recent_blockhash)
            transaction = Transaction.new_unsigned(message)

            # 签名交易
            transaction.sign([self.wallet_keypair], recent_blockhash)
            logger.info("✅ 销毁交易已签名")

            # 发送交易
            if not self.provider:
                raise RuntimeError("Provider未初始化")

            opts = TxOpts(skip_confirmation=False, preflight_commitment=Confirmed)
            response = await self.provider.send(transaction, opts)

            if response:
                logger.info(f"✅ 销毁成功！NFT #{nft_id}, 交易签名: {response}")
                return {
                    "success": True,
                    "transaction_signature": str(response),
                    "nft_id": nft_id,
                    "message": "销毁成功"
                }
            else:
                return {
                    "success": False,
                    "nft_id": nft_id,
                    "error_message": "交易发送失败"
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ 销毁NFT #{burn_params.get('nft_id', 'unknown')} 失败: {error_msg}")

            # 尝试解析错误代码
            if hasattr(e, 'args') and e.args:
                error_str = str(e.args[0])
                if "custom program error:" in error_str.lower():
                    try:
                        error_code = int(error_str.split("custom program error: 0x")[1][:4], 16)
                        error_message = self.get_error_message(error_code)
                        if error_message:
                            error_msg = f"{error_message} (错误代码: {error_code})"
                    except:
                        pass
            else:
                logger.error(e)
            return {
                "success": False,
                "nft_id": burn_params.get("nft_id"),
                "error_message": error_msg
            }

    async def build_burn_transaction_with_anchor(self, burn_params: Dict[str, Any]):
        """使用Anchor构建销毁交易"""
        try:
            # 从burn_params中提取参数
            nft_id = burn_params["nft_id"]
            project_mint_str = burn_params["project_mint"]
            dry_run = burn_params.get("dry_run", True)

            logger.info(f"🔨 使用Anchor构建销毁交易，NFT ID: {nft_id}")

            if not self.program:
                raise RuntimeError("程序未初始化")

            # 转换项目mint地址
            project_mint = Pubkey.from_string(project_mint_str)
            logger.info(f"项目mint: {project_mint}")

            # 计算PDA地址
            pdas = self.calculate_pda_addresses(project_mint, nft_id)

            # 准备用户账户
            payer = self.wallet_keypair.pubkey()

            logger.info("✅ 使用Anchor构建销毁指令")

            # 使用Anchor的RPC API构建销毁交易
            from anchorpy.program.context import Context

            # 构建销毁指令的上下文
            ctx = Context(
                accounts={
                    "nft": pdas["nft"],
                    "authority": pdas["pool_auth"],  # 使用pool_auth作为authority
                    "mint": project_mint,
                    "output_vault": pdas["pool_auth_project_ata"],  # 项目代币库
                    "output_token_account": get_associated_token_address(payer, project_mint),  # 用户的项目代币账户
                    "project": pdas["project"],
                    "payer": payer,
                    "token_program": TOKEN_PROGRAM_ID,
                    "associated_token_program": ASSOCIATED_TOKEN_PROGRAM_ID,
                    "system_program": SYSTEM_PROGRAM_ID
                }
            )

            if dry_run:
                # 使用simulate进行测试
                simulate_result = await self.program.simulate["burn"](ctx=ctx)
                logger.info(f"✅ 销毁Simulate结果: {simulate_result}")
                return None  # 干运行不返回实际指令
            else:
                # 构建实际指令
                instruction = self.program.instruction["burn"](ctx=ctx)
                logger.info("✅ 销毁指令构建成功")
                return instruction

        except Exception as e:
            logger.error(f"❌ 构建销毁交易失败: {e}")
            raise
    async def split_nft(self, split_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        分割NFT

        Args:
            split_params: 分割参数字典，包含：
                - nft_id (int): 要分割的NFT ID
                - project_mint (str): 项目mint地址
                - dry_run (bool, optional): 是否为干运行模式，默认True
        """
        try:
            # 提取必需参数进行基本验证
            nft_id = split_params["nft_id"]
            project_mint_str = split_params["project_mint"]
            dry_run = split_params.get("dry_run", True)

            logger.info(f"✂️ 开始分割NFT #{nft_id}")

            # 验证NFT是否可以分割
            nft_info = self.find_nft_by_id(nft_id, project_mint_str)
            if not nft_info:
                raise ValueError(f"未找到NFT #{nft_id}")

            # 构建指令（使用Anchor最佳实践）
            instruction = await self.build_split_transaction_with_anchor(split_params, nft_info)

            # 检查是否为干运行模式
            if dry_run:
                logger.info("🔍 干运行模式，不会实际发送交易")
                return {
                    "success": True,
                    "transaction_signature": "dry_run_simulation",
                    "nft_id": nft_id,
                    "message": "分割干运行成功"
                }

            # 创建交易并签名
            if not self.client:
                raise RuntimeError("客户端未初始化")

            recent_blockhash_resp = await self.client.get_latest_blockhash()
            recent_blockhash = recent_blockhash_resp.value.blockhash

            # 创建交易
            from solders.message import Message
            payer = self.wallet_keypair.pubkey()
            message = Message.new_with_blockhash([instruction], payer, recent_blockhash)
            transaction = Transaction.new_unsigned(message)

            # 签名交易
            transaction.sign([self.wallet_keypair], recent_blockhash)
            logger.info("✅ 交易已签名")

            # 发送交易
            if not self.provider:
                raise RuntimeError("Provider未初始化")

            opts = TxOpts(skip_confirmation=False, preflight_commitment=Confirmed)
            response = await self.provider.send(transaction, opts)

            if response:
                logger.info(f"✅ 分割成功！NFT #{nft_id}, 交易签名: {response}")
                return {
                    "success": True,
                    "transaction_signature": str(response),
                    "nft_id": nft_id,
                    "message": "分割成功"
                }
            else:
                return {
                    "success": False,
                    "nft_id": nft_id,
                    "error_message": "交易发送失败"
                }

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ 分割NFT #{split_params.get('nft_id', 'unknown')} 失败: {error_msg}")

            # 尝试解析错误代码
            if hasattr(e, 'args') and e.args:
                error_str = str(e.args[0])
                if "custom program error:" in error_str.lower():
                    try:
                        error_code = int(error_str.split("custom program error: 0x")[1][:4], 16)
                        error_message = self.get_error_message(error_code)
                        if error_message:
                            error_msg = f"{error_message} (错误代码: {error_code})"
                    except:
                        pass
            else:
                logger.error(e)
            return {
                "success": False,
                "nft_id": split_params.get("nft_id"),
                "error_message": error_msg
            }

    async def build_split_transaction_with_anchor(self, split_params: Dict[str, Any], nft_info: Dict[str, Any]):
        """使用Anchor构建分割交易"""
        try:
            # 从split_params中提取参数
            nft_id = split_params["nft_id"]
            project_mint_str = split_params["project_mint"]
            dry_run = split_params.get("dry_run", True)

            logger.info(f"🔨 使用Anchor构建分割交易，NFT ID: {nft_id}")

            if not self.program:
                raise RuntimeError("程序未初始化")

            # 转换项目mint地址
            project_mint = Pubkey.from_string(project_mint_str)
            logger.info(f"项目mint: {project_mint}")

            # 计算PDA地址
            pdas = await self.calculate_split_pda_addresses(project_mint, nft_id)

            # 准备用户账户
            payer = self.wallet_keypair.pubkey()
            old_nft = Pubkey.from_string(nft_info["nft_pubkey"])

            logger.info("✅ 使用Anchor构建分割指令")

            # 使用Anchor的RPC API构建分割交易
            from anchorpy.program.context import Context

            # 构建分割指令的上下文
            ctx = Context(
                accounts={
                    "new_nft": pdas["new_nft"],
                    "old_nft": old_nft,
                    "project": pdas["project"],
                    "payer": payer,
                    "system_program": SYSTEM_PROGRAM_ID
                }
            )

            if dry_run:
                # 使用simulate进行测试
                simulate_result = await self.program.simulate["split"](ctx=ctx)
                logger.info(f"✅ 分割Simulate结果: {simulate_result}")
                return None  # 干运行不返回实际指令
            else:
                # 构建实际指令
                instruction = self.program.instruction["split"](ctx=ctx)
                logger.info("✅ 分割指令构建成功")
                return instruction

        except Exception as e:
            logger.error(f"❌ 构建分割交易失败: {e}")
            raise

    async def calculate_split_pda_addresses(self, project_mint: Pubkey, nft_id: int) -> Dict[str, Pubkey]:
        """计算分割操作所需的PDA地址"""
        pdas = {}

        # 计算项目PDA: ["ppp_project", project_mint]
        project_seeds = [b"ppp_project", bytes(project_mint)]
        project_pda, _ = Pubkey.find_program_address(project_seeds, self.program_id)
        pdas["project"] = project_pda

        # 从API获取项目的当前nft_id
        try:
            from ppp_api import PPPAPIClient

            async with PPPAPIClient() as api_client:
                # 使用现有的 /projects 端点获取项目列表
                endpoint = "/projects"
                params = {"limit": 100, "page": 1}

                raw_data = await api_client._request("GET", endpoint, params=params)
                projects_data = raw_data.get("data", [])

                # 查找匹配的项目（project_mint 就是 mint_pubkey）
                target_project_data = None
                for item in projects_data:
                    project_mint_from_api = item.get("mint_pubkey")
                    if project_mint_from_api == str(project_mint):
                        target_project_data = item
                        break

                if target_project_data:
                    # 从项目数据中获取当前的nft_id
                    current_nft_id = target_project_data.get("nft_id", 0)

                    # 新NFT的ID应该是当前nft_id + 1
                    new_nft_id = current_nft_id + 1
                    logger.info(f"从项目API获取到当前nft_id: {current_nft_id}, 新NFT ID: {new_nft_id}")
                else:
                    logger.warning(f"未找到项目: {project_mint}")
                    raise ValueError(f"未找到项目: {project_mint}")

        except Exception as e:
            logger.warning(f"无法从API获取项目nft_id，使用备用方案: {e}")
            # 备用方案：假设新NFT ID是当前NFT ID + 1000（避免冲突）
            new_nft_id = nft_id + 1000
            logger.info(f"使用备用新NFT ID: {new_nft_id}")

        # 计算新NFT的PDA: ["ppp_nft", new_nft_id, project]
        new_nft_id_bytes = new_nft_id.to_bytes(8, byteorder='little')
        new_nft_seeds = [b"ppp_nft", new_nft_id_bytes, bytes(project_pda)]
        new_nft_pda, _ = Pubkey.find_program_address(new_nft_seeds, self.program_id)
        pdas["new_nft"] = new_nft_pda

        logger.info(f"计算的PDA地址:")
        logger.info(f"  项目: {project_pda}")
        logger.info(f"  新NFT ID: {new_nft_id}")
        logger.info(f"  新NFT: {new_nft_pda}")

        return pdas







    def load_nft_data(self, project_mint: Optional[str] = None) -> Dict[str, Any]:
        """加载NFT数据

        Args:
            project_mint: 项目mint地址，如果提供则加载对应项目的缓存
        """
        if project_mint:
            # 使用项目特定的缓存文件
            nfts_path = Path(f"api_result/nfts_{project_mint}.json")
        else:
            # 回退到默认文件（向后兼容）
            nfts_path = Path("api_result/nfts.json")

        if not nfts_path.exists():
            raise FileNotFoundError(f"找不到缓存文件: {nfts_path}")

        with open(nfts_path, 'r') as f:
            data = json.load(f)

        return data

    async def find_nft_by_id_from_api(self, project_mint: str, nft_id: int) -> Optional[Dict[str, Any]]:
        """从API获取NFT信息"""
        try:
            from ppp_api import PPPAPIClient

            async with PPPAPIClient() as api_client:
                # 获取项目的所有NFT
                nfts = await api_client.get_project_nfts(project_mint)

                # 查找指定ID的NFT
                for nft in nfts:
                    if nft.id == nft_id:
                        # 转换为兼容格式
                        nft_data = {
                            "nft_id": nft.id,
                            "nft_pubkey": nft.mint_address,
                            "owner_pubkey": nft.owner,
                            "mint_pubkey": project_mint,
                            "price": int(nft.price * 10**9),  # 转换为lamports
                            "round": nft.attributes.get("round", 0),
                            "split_count": nft.attributes.get("split_count", 0),
                            "last_trade": nft.attributes.get("last_trade", 0),
                            "create_time": nft.attributes.get("create_time", ""),
                            "bump": nft.attributes.get("bump", 0)
                        }
                        logger.info(f"✅ 从API获取NFT #{nft_id}数据成功")
                        return nft_data

                logger.warning(f"⚠️ API中未找到NFT #{nft_id}")
                return None

        except Exception as e:
            logger.warning(f"⚠️ 从API获取NFT数据失败: {e}")
            # 回退到本地缓存
            return self.find_nft_by_id_from_cache(nft_id, project_mint)

    def find_nft_by_id_from_cache(self, nft_id: int, project_mint: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """从本地缓存查找NFT信息（回退方案）

        Args:
            nft_id: NFT ID
            project_mint: 项目mint地址，如果提供则从对应项目的缓存中查找
        """
        try:
            # 如果提供了项目mint，优先从项目特定缓存查找
            if project_mint:
                try:
                    nft_data = self.load_nft_data(project_mint)
                    for nft in nft_data.get("data", []):
                        if nft.get("nft_id") == nft_id:
                            logger.info(f"✅ 从项目缓存找到NFT #{nft_id} (项目: {project_mint})")
                            return nft
                except FileNotFoundError:
                    logger.debug(f"项目缓存文件不存在: {project_mint}")

            # 回退到默认缓存文件
            try:
                nft_data = self.load_nft_data()
                for nft in nft_data.get("data", []):
                    if nft.get("nft_id") == nft_id:
                        logger.info(f"✅ 从默认缓存找到NFT #{nft_id}")
                        return nft
            except FileNotFoundError:
                logger.debug("默认缓存文件不存在")

            # 如果没有指定项目mint或上述方法都失败，搜索所有缓存文件
            logger.info(f"🔍 在所有缓存文件中搜索NFT #{nft_id}...")
            api_result_dir = Path("api_result")
            if api_result_dir.exists():
                for cache_file in api_result_dir.glob("nfts_*.json"):
                    try:
                        with open(cache_file, 'r') as f:
                            cache_data = json.load(f)
                        for nft in cache_data.get("data", []):
                            if nft.get("nft_id") == nft_id:
                                project_mint_found = cache_data.get("project_mint", cache_file.stem.replace("nfts_", ""))
                                logger.info(f"✅ 在缓存文件中找到NFT #{nft_id} (项目: {project_mint_found})")
                                return nft
                    except Exception as e:
                        logger.debug(f"读取缓存文件失败 {cache_file}: {e}")

            logger.warning(f"⚠️ 本地缓存中未找到NFT #{nft_id}")
            return None
        except Exception as e:
            logger.error(f"❌ 读取本地NFT缓存失败: {e}")
            return None

    def find_nft_by_id(self, nft_id: int, project_mint: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """根据NFT ID查找NFT信息（从缓存获取）

        Args:
            nft_id: NFT ID
            project_mint: 项目mint地址，如果提供则从对应项目的缓存中查找
        """
        return self.find_nft_by_id_from_cache(nft_id, project_mint)

    async def get_or_create_ata(self, mint: Pubkey, owner: Pubkey) -> Pubkey:
        """获取或创建关联代币账户"""
        ata = get_associated_token_address(owner, mint)

        # 检查账户是否存在
        try:
            account_info = await self.client.get_account_info(ata)
            if account_info.value is None:
                # 账户不存在，需要创建
                logger.info(f"创建关联代币账户: {ata}")
                # 注意：这里只返回地址，实际创建指令会在交易中添加
        except Exception as e:
            logger.warning(f"检查ATA时出错: {e}")

        return ata

    def get_referral_address(self) -> str:
        """根据当前钱包地址确定推荐人地址"""
        wallet_address = str(self.wallet_keypair.pubkey())

        if wallet_address == self.default_referral:
            # 如果当前钱包是默认推荐人，使用备用推荐人
            referral = self.fallback_referral
            logger.info(f"当前钱包是默认推荐人，使用备用推荐人: {referral}")
        else:
            # 否则使用默认推荐人
            referral = self.default_referral
            logger.info(f"使用默认推荐人: {referral}")

        return referral

    def _load_error_codes(self) -> Dict[int, Dict[str, str]]:
        """从完整ABI加载错误代码映射"""
        try:
            import json
            from pathlib import Path

            # 读取完整ABI文件
            abi_complete_path = Path(__file__).parent / "abi_complete.json"
            if not abi_complete_path.exists():
                logger.warning("完整ABI文件不存在，使用默认错误处理")
                return {}

            with open(abi_complete_path, 'r', encoding='utf-8') as f:
                abi_complete = json.load(f)

            # 提取错误定义
            error_codes = {}
            if "errors" in abi_complete:
                for error in abi_complete["errors"]:
                    code = error.get("code")
                    name = error.get("name", "Unknown")
                    msg = error.get("msg", "Unknown error")

                    if code is not None:
                        error_codes[code] = {
                            "name": name,
                            "message": msg
                        }

            logger.info(f"✅ 加载了 {len(error_codes)} 个错误代码定义")
            return error_codes

        except Exception as e:
            logger.error(f"加载错误代码失败: {e}")
            return {}

    def get_error_message(self, error_code: int) -> str:
        """根据错误代码获取错误信息"""
        if error_code in self.error_codes:
            error_info = self.error_codes[error_code]
            return f"{error_info['name']}: {error_info['message']}"
        else:
            return f"Unknown error code: {error_code}"



    def calculate_pda_addresses(self, project_mint: Pubkey, nft_id: int) -> Dict[str, Pubkey]:
        """计算v2所需的PDA地址"""
        pdas = {}

        # v2只需要authority PDA: ["ppp_auth", project.mint]
        authority_seeds = [b"ppp_auth", bytes(project_mint)]
        authority_pda, _ = Pubkey.find_program_address(authority_seeds, self.program_id)
        pdas["authority"] = authority_pda

        return pdas

    async def buy_nft(self, buy_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        买入NFT

        Args:
            buy_params: 买入参数字典，包含：
                - nft_id (int): NFT ID
                - creator (str): 项目creator地址
                - dry_run (bool, optional): 是否为干运行模式，默认True
                - enable_validation (bool, optional): 是否启用验证，默认False
                - project_mint (str, optional): 项目mint地址
        """
        try:
            # 提取必需参数进行基本验证
            nft_id = buy_params["nft_id"]

            logger.info(f"🎯 开始购买NFT #{nft_id}")

            # 构建指令（使用Anchor最佳实践）
            instruction = await self.build_buy_transaction_with_anchor(buy_params)

            # 检查是否为干运行模式
            dry_run = buy_params.get("dry_run", True)
            if dry_run:
                logger.info("🔍 干运行模式，不会实际发送交易")
                return {
                    "success": True,
                    "transaction_signature": "dry_run_simulation",
                    "nft_id": nft_id,
                    "message": "干运行成功"
                }

            # 创建交易并签名
            if not self.client:
                raise RuntimeError("客户端未初始化")

            recent_blockhash_resp = await self.client.get_latest_blockhash()
            recent_blockhash = recent_blockhash_resp.value.blockhash

            # 创建交易
            from solders.message import Message
            payer = self.wallet_keypair.pubkey()
            message = Message.new_with_blockhash([instruction], payer, recent_blockhash)
            transaction = Transaction.new_unsigned(message)

            # 签名交易
            transaction.sign([self.wallet_keypair], recent_blockhash)
            logger.info("✅ 交易已签名")

            # 发送交易
            if not self.provider:
                raise RuntimeError("Provider未初始化")

            opts = TxOpts(skip_confirmation=False, preflight_commitment=Confirmed)
            response = await self.provider.send(transaction, opts)

            if response:
                logger.info(f"✅ 购买成功！NFT #{nft_id}, 交易签名: {response}")
                return {
                    "success": True,
                    "transaction_signature": str(response),
                    "nft_id": nft_id,
                    "message": "购买成功"
                }
            else:
                return {
                    "success": False,
                    "error_message": "交易发送失败",
                    "nft_id": nft_id
                }
        except SolanaRpcException as e:
            logger.info("RPC 异常")
            return {
                "success": False,
                "error_message": "RPC 异常",
                "nft_id": nft_id
            }
        except RPCException as e:
            # 安全地检查错误代码
            error_code = None
            try:
                if (hasattr(e, 'args') and len(e.args) > 0 and
                    hasattr(e.args[0], 'data') and
                    hasattr(e.args[0].data, 'err') and
                    hasattr(e.args[0].data.err, 'err') and
                    hasattr(e.args[0].data.err.err, 'code')):
                    error_code = e.args[0].data.err.err.code
            except (AttributeError, IndexError, TypeError):
                pass

            if error_code in [6028, 6024]:
                logger.info("NFT 不可交易")
                return {
                    "success": False,
                    "error_message": "NFT 不可交易",
                    "nft_id": nft_id
                }
            elif error_code:
                # 使用完整ABI中的错误信息
                error_message = self.get_error_message(error_code)
                logger.error(f"程序错误 {error_code}: {error_message}")
                return {
                    "success": False,
                    "error_code": error_code,
                    "error_message": error_message,
                    "nft_id": nft_id
                }
            else:
                logger.error(f"RPC异常: {e}")
                return {
                    "success": False,
                    "error_message": f"RPC异常: {e}",
                    "nft_id": nft_id
                }
        except ProgramError as e:
            if e.code in [6028, 6024]:
                logger.info("NFT 不可交易")
            else:
                logger.error( e)
            return {
                "success": False,
                "error_message": e,
                "nft_id": nft_id
            }
        except Exception as e:
            logger.error(e)
            return {
                "success": False,
                "error_message": e,
                "nft_id": nft_id
            }

    async def build_buy_transaction_with_anchor(self, buy_params: Dict[str, Any], nft_info: Optional[Dict[str, Any]] = None):
        """使用Anchor构建买入交易 - 最佳实践"""
        try:
            # 从buy_params中提取参数
            nft_id = buy_params["nft_id"]
            creator = buy_params["creator"]
            project_mint_str = buy_params.get("project_mint")
            project_id_str = buy_params.get("project_id")
            enable_validation = buy_params.get("enable_validation", False)
            dry_run = buy_params.get("dry_run", True)

            logger.info(f"🔨 使用Anchor构建买入交易，NFT ID: {nft_id}")

            if not self.program:
                raise RuntimeError("程序未初始化")

            # 1. 加载NFT数据（优先使用传入的数据，然后尝试缓存，最后回退到API）
            if not nft_info:
                # 优先从缓存获取
                nft_info = self.find_nft_by_id(nft_id, project_mint_str)

                if not nft_info:
                    raise ValueError(f"未找到NFT #{nft_id}")

            logger.debug(f"NFT信息: {nft_info}")

            # 2. 获取项目mint地址 - 应该从参数中获取，不是从NFT信息中
            logger.debug(f"NFT信息字段: {list(nft_info.keys())}")
            if not project_mint_str:
                raise ValueError("project_mint参数是必需的")

            project_mint_pubkey = Pubkey.from_string(project_mint_str)
            logger.info(f"项目mint: {project_mint_pubkey}")

            # 3. 获取creator和referral参数
            if not creator:
                raise ValueError(f"creator参数是必需的，请从配置中传入")

            logger.info(f"Creator: {creator}")
            logger.info(f"Referral: {self.referral_address}")

            # 4. 计算v2的简化PDA地址
            pdas = self.calculate_pda_addresses(project_mint_pubkey, nft_id)

            # 5. 准备账户
            payer = self.wallet_keypair.pubkey()
            project = Pubkey.from_string(project_id_str)
            nft_pubkey = Pubkey.from_string(nft_info["nft_pubkey"])
            current_owner = Pubkey.from_string(nft_info["owner_pubkey"])
            creator_pubkey = Pubkey.from_string(creator)
            protocol_fee_pubkey = Pubkey.from_string(self.protocol_fee_address)
            referral_pubkey = Pubkey.from_string(self.referral_address)
            system_program = Pubkey.from_string("********************************")

            logger.info("✅ 使用Anchor构建v2买入指令")

            # 6. 使用v2的简化账户结构
            from anchorpy.program.context import Context

            ctx = Context(
                accounts={
                    "payer": payer,
                    "project": project,
                    "authority": pdas["authority"],
                    "nft": nft_pubkey,
                    "current_owner": current_owner,
                    "protocol_fee": protocol_fee_pubkey,
                    "referral": referral_pubkey,
                    "creator": creator_pubkey,
                    "system_program": system_program,
                }
            )

            # 可选的参数对比验证（仅在测试模式下）
            if enable_validation:
                await self.compare_parameters_with_expected(ctx.accounts)

            if dry_run:
                # 使用simulate进行测试
                try:
                    simulate_result = await self.program.simulate["buy"](ctx=ctx)
                    logger.info(f"✅ Simulate结果: {simulate_result}")
                except Exception as sim_error:
                    logger.warning(f"⚠️ 模拟失败，但继续构建交易: {sim_error}")
                    # 继续构建交易，不中断流程

            # 构建实际指令
            instruction = self.program.instruction["buy"](ctx=ctx)

            logger.info("✅ Anchor指令构建完成")
            return instruction
        except SolanaRpcException as e:
            logger.info("RPC 异常")
            raise e
        except Exception as e:
            import traceback
            logger.error(f"Anchor交易构建失败: {e}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise e

    async def compare_parameters_with_expected(self, calculated_accounts: Dict[str, Any],
                                             config_file: str = "expected_values/7X7Qhq4eLmMVfkUyLxZheaQ9oSaGDazNpWFUJfsjSppp_105.json"):
        """对比计算出的参数与期望值"""
        logger.info("🔍 开始参数对比验证...")

        # camelCase到snake_case的映射
        case_mapping = {
            "ammConfig": "amm_config",
            "poolState": "pool_state",
            "inputVault": "input_vault",
            "outputVault": "output_vault",
            "observationState": "observation_state",
            "poolWsolVault": "pool_wsol_vault",
            "cpSwapProgram": "cp_swap_program",
            "inputTokenMint": "input_token_mint",
            "outputTokenMint": "output_token_mint",
            "currentOwner": "current_owner",
            "wsolMint": "wsol_mint",
            "protocolFee": "protocol_fee",
            "inputTokenAccount": "input_token_account",
            "outputTokenAccount": "output_token_account",
            "inputTokenProgram": "input_token_program",
            "outputTokenProgram": "output_token_program",
            "systemProgram": "system_program",
            "tokenProgram": "token_program"
        }

        # 从配置文件加载期望值
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 支持新的简化格式（直接是参数对象）和旧格式（包含expected_parameters）
            if "expected_parameters" in config:
                raw_expected_values = config["expected_parameters"]
            else:
                raw_expected_values = config

            # 转换camelCase到snake_case
            expected_values = {}
            for camel_key, value in raw_expected_values.items():
                snake_key = case_mapping.get(camel_key, camel_key)
                expected_values[snake_key] = value

            # 用户特定参数在代码中控制，不从配置文件读取
            user_specific_accounts = ["payer"]

            logger.info(f"📁 从 {config_file} 加载期望值配置")
        except Exception as e:
            logger.error(f"❌ 无法加载期望值配置文件 {config_file}: {e}")
            return {"matches": 0, "total": 0, "match_rate": 0, "mismatches": []}

        matches = 0
        total = 0
        mismatches = []

        logger.info("📊 参数对比结果:")
        for key, expected in expected_values.items():
            if key in calculated_accounts:
                calculated = str(calculated_accounts[key])
                total += 1

                if calculated == expected:
                    matches += 1
                    logger.info(f"✅ {key}: 匹配")
                else:
                    mismatches.append({
                        "parameter": key,
                        "expected": expected,
                        "calculated": calculated
                    })
                    logger.warning(f"❌ {key}:")
                    logger.warning(f"   期望: {expected}")
                    logger.warning(f"   计算: {calculated}")

        # 检查用户特定的账户
        for key in user_specific_accounts:
            if key in calculated_accounts:
                logger.info(f"ℹ️  {key}: {calculated_accounts[key]} (用户特定)")

        logger.info(f"🎯 匹配率: {matches}/{total} ({matches/total*100:.1f}%)")

        if mismatches:
            logger.warning("⚠️  不匹配的参数:")
            for mismatch in mismatches:
                logger.warning(f"   {mismatch['parameter']}: 期望 {mismatch['expected']}, 得到 {mismatch['calculated']}")

        return {
            "matches": matches,
            "total": total,
            "match_rate": matches/total if total > 0 else 0,
            "mismatches": mismatches
        }

# 使用示例
async def main():
    """主函数示例"""
    # 请替换为你的私钥
    private_key = "your_private_key_here"

    async with PPPSimpleBuyer(private_key) as buyer:
        # 测试购买NFT #83
        # 注意：现在使用字典参数格式
        buy_params = {
            "nft_id": 83,
            "creator": "your_creator_address_here",
            "dry_run": True
        }
        result = await buyer.buy_nft(buy_params)
        print(f"购买结果: {result}")

        # 测试销毁NFT
        burn_params = {
            "nft_id": 209,
            "project_mint": "5ycfxnqkPguANGMKqmjKFNhEiX4sntwmps7CE7ZEUppp",
            "dry_run": True
        }
        burn_result = await buyer.burn_nft(burn_params)
        print(f"销毁结果: {burn_result}")

        # 测试分割NFT
        split_params = {
            "nft_id": 105,
            "project_mint": "7X7Qhq4eLmMVfkUyLxZheaQ9oSaGDazNpWFUJfsjSppp",
            "dry_run": True
        }
        split_result = await buyer.split_nft(split_params)
        print(f"分割结果: {split_result}")

if __name__ == "__main__":
    asyncio.run(main())

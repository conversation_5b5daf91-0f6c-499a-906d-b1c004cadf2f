{"version": "0.1.0", "name": "pppfun", "instructions": [{"name": "burn", "accounts": [{"name": "nft", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "outputVault", "isMut": true, "isSigner": false}, {"name": "outputTokenAccount", "isMut": true, "isSigner": false}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "associatedTokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "buy", "accounts": [{"name": "cpSwapProgram", "isMut": false, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "authority", "isMut": false, "isSigner": false}, {"name": "ammConfig", "isMut": false, "isSigner": false}, {"name": "poolState", "isMut": true, "isSigner": false}, {"name": "inputTokenAccount", "isMut": true, "isSigner": false}, {"name": "outputTokenAccount", "isMut": true, "isSigner": false}, {"name": "inputVault", "isMut": true, "isSigner": false}, {"name": "outputVault", "isMut": true, "isSigner": false}, {"name": "inputTokenProgram", "isMut": false, "isSigner": false}, {"name": "outputTokenProgram", "isMut": false, "isSigner": false}, {"name": "inputTokenMint", "isMut": false, "isSigner": false}, {"name": "outputTokenMint", "isMut": false, "isSigner": false}, {"name": "observationState", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "nft", "isMut": true, "isSigner": false}, {"name": "current<PERSON>wner", "isMut": true, "isSigner": false}, {"name": "wsolMint", "isMut": false, "isSigner": false}, {"name": "poolWsolVault", "isMut": true, "isSigner": false}, {"name": "poolAuth", "isMut": true, "isSigner": false}, {"name": "protocolFee", "isMut": true, "isSigner": false}, {"name": "referral", "isMut": true, "isSigner": false, "optional": true}, {"name": "creator", "isMut": true, "isSigner": false}], "args": []}, {"name": "create", "accounts": [{"name": "newProject", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": false}, {"name": "metadata", "isMut": true, "isSigner": false}, {"name": "tokenMetadataProgram", "isMut": false, "isSigner": false}, {"name": "mint", "isMut": true, "isSigner": true}, {"name": "pool<PERSON><PERSON>Vault", "isMut": true, "isSigner": false}, {"name": "wsolMint", "isMut": false, "isSigner": false}, {"name": "poolWsolVault", "isMut": true, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "associatedTokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "projectName", "type": "string"}, {"name": "projectDesc", "type": "string"}, {"name": "tokenSymbol", "type": "string"}, {"name": "tokenUri", "type": "string"}, {"name": "initNftPrice", "type": "u64"}, {"name": "increasePerRound", "type": "u64"}, {"name": "tradeFeeRate", "type": "u64"}, {"name": "secPerRound", "type": "u64"}, {"name": "secToBurnNft", "type": "u64"}]}, {"name": "mint", "accounts": [{"name": "cpSwapProgram", "isMut": false, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "creator", "isMut": true, "isSigner": false}, {"name": "ammConfig", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": false}, {"name": "poolState", "isMut": true, "isSigner": false}, {"name": "token0Mint", "isMut": false, "isSigner": false}, {"name": "token1Mint", "isMut": false, "isSigner": false}, {"name": "lpMint", "isMut": true, "isSigner": false}, {"name": "creatorToken0", "isMut": true, "isSigner": false}, {"name": "creatorToken1", "isMut": true, "isSigner": false}, {"name": "creatorLpToken", "isMut": true, "isSigner": false}, {"name": "token0Vault", "isMut": true, "isSigner": false}, {"name": "token1Vault", "isMut": true, "isSigner": false}, {"name": "createPoolFee", "isMut": true, "isSigner": false}, {"name": "observationState", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "token0Program", "isMut": false, "isSigner": false}, {"name": "token1Program", "isMut": false, "isSigner": false}, {"name": "associatedTokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "newNft", "isMut": true, "isSigner": false}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "wsolMint", "isMut": false, "isSigner": false}, {"name": "poolWsolVault", "isMut": true, "isSigner": false}, {"name": "protocolFee", "isMut": true, "isSigner": false}], "args": []}, {"name": "split", "accounts": [{"name": "newNft", "isMut": true, "isSigner": false}, {"name": "oldNft", "isMut": true, "isSigner": false}, {"name": "project", "isMut": true, "isSigner": false}, {"name": "payer", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}], "accounts": [], "types": [], "events": [], "errors": [], "constants": []}